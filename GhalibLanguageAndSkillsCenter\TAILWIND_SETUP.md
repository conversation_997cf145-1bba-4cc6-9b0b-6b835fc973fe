# Tailwind CSS Setup Guide

## ✅ Installation Complete

Tailwind CSS has been successfully installed and configured in your Blazor project!

## 📁 Files Created/Modified

### New Files:
- `tailwind.config.js` - Tailwind configuration
- `src/input.css` - Tailwind input file
- `wwwroot/css/tailwind.css` - Generated Tailwind CSS (auto-generated)
- `TAILWIND_SETUP.md` - This documentation

### Modified Files:
- `package.json` - Added Tailwind dependencies and build scripts
- `Components/App.razor` - Added Tailwind CSS link
- `Components/Pages/Counter.razor` - Added example Tailwind classes

## 🚀 Available NPM Scripts

```bash
# Build Tailwind CSS once
npm run build-css

# Watch for changes and rebuild automatically
npm run watch-css

# Build minified version for production
npm run build-css-prod
```

## 🎨 How to Use Tailwind CSS

### 1. Development Workflow
When developing, run the watch command to automatically rebuild CSS when you add new Tailwind classes:
```bash
npm run watch-css
```

### 2. Adding Tailwind Classes
You can now use Tailwind utility classes in your Razor components:

```html
<div class="bg-blue-500 text-white p-4 rounded-lg shadow-md">
    <h1 class="text-2xl font-bold mb-2">Hello Tailwind!</h1>
    <p class="text-blue-100">This is styled with Tailwind CSS</p>
</div>
```

### 3. Common Tailwind Patterns

#### Responsive Design:
```html
<div class="w-full md:w-1/2 lg:w-1/3">
    <!-- Responsive width -->
</div>
```

#### Hover Effects:
```html
<button class="bg-blue-500 hover:bg-blue-700 transition duration-300">
    Hover me
</button>
```

#### Flexbox Layout:
```html
<div class="flex items-center justify-between p-4">
    <span>Left content</span>
    <span>Right content</span>
</div>
```

## 🔧 Configuration

### Content Paths
The `tailwind.config.js` is configured to scan these file types for Tailwind classes:
- `**/*.razor`
- `**/*.cshtml`
- `**/*.html`

### Plugins Included
- `@tailwindcss/typography` - Better typography styles
- `@tailwindcss/forms` - Form styling
- `@tailwindcss/aspect-ratio` - Aspect ratio utilities

## 📝 Testing

Visit `/counter` page to see Tailwind CSS in action! The page now includes:
- Styled card layout
- Gradient background
- Hover effects
- Responsive design

## 🔄 Production Build

For production, use the minified build:
```bash
npm run build-css-prod
```

## 💡 Tips

1. **Purging**: Tailwind automatically removes unused CSS in production
2. **Custom Styles**: Add custom CSS to `src/input.css` after the Tailwind directives
3. **RTL Support**: Your project uses RTL layout - Tailwind works well with RTL
4. **IntelliSense**: Install "Tailwind CSS IntelliSense" VS Code extension for autocomplete

## 🆘 Troubleshooting

If styles aren't applying:
1. Make sure you've run `npm run build-css`
2. Check that `wwwroot/css/tailwind.css` exists
3. Verify the CSS link in `App.razor`
4. Clear browser cache

Happy styling with Tailwind CSS! 🎨
