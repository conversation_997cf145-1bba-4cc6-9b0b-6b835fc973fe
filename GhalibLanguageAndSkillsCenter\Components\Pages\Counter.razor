@page "/counter"
@rendermode InteractiveServer

<PageTitle>Counter</PageTitle>

<AuthorizeView Roles="Administrator">
<div class="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl m-4">
    <div class="p-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Counter</h1>

        <p role="status" class="text-lg text-gray-600 mb-6">Current count: <span class="font-semibold text-blue-600">@currentCount</span></p>

        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out transform hover:scale-105" @onclick="IncrementCount">
            Click me
        </button>

        <!-- Tailwind CSS test elements -->
        <div class="mt-6 p-4 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 rounded-lg">
            <p class="text-white font-semibold">🎉 Tailwind CSS is working!</p>
        </div>
    </div>
</div>
</AuthorizeView>

@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }
}
