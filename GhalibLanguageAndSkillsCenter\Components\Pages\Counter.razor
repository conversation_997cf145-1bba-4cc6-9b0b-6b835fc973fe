﻿@page "/counter"
@rendermode InteractiveServer

<PageTitle>Counter</PageTitle>

<AuthorizeView Roles="Administrator">
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">صفحه تست طراحی جدید</h1>
        <p class="text-gray-600">این صفحه برای تست طراحی جدید با Tailwind CSS است</p>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

        <!-- Counter Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center mb-4">
                <div class="bg-blue-100 p-3 rounded-full">
                    <span class="bi bi-calculator text-blue-600 text-xl"></span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 mr-3">شمارنده</h2>
            </div>

            <p role="status" class="text-lg text-gray-600 mb-4">
                تعداد فعلی: <span class="font-bold text-blue-600 text-2xl">@currentCount</span>
            </p>

            <button class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 ease-in-out transform hover:scale-105 shadow-md" @onclick="IncrementCount">
                افزایش شمارنده
            </button>
        </div>

        <!-- Success Message Card -->
        <div class="bg-gradient-to-br from-green-400 to-blue-500 rounded-lg shadow-md p-6 text-white">
            <div class="flex items-center mb-4">
                <span class="bi bi-check-circle text-2xl ml-3"></span>
                <h2 class="text-xl font-semibold">موفقیت!</h2>
            </div>
            <p class="text-green-100 mb-4">Tailwind CSS با موفقیت نصب و پیکربندی شد</p>
            <div class="bg-white/20 rounded-lg p-3">
                <p class="text-sm">✅ طراحی واکنش‌گرا</p>
                <p class="text-sm">✅ پشتیبانی از RTL</p>
                <p class="text-sm">✅ انیمیشن‌های روان</p>
            </div>
        </div>

        <!-- Features Card -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center mb-4">
                <div class="bg-purple-100 p-3 rounded-full">
                    <span class="bi bi-star text-purple-600 text-xl"></span>
                </div>
                <h2 class="text-xl font-semibold text-gray-900 mr-3">ویژگی‌ها</h2>
            </div>

            <ul class="space-y-3">
                <li class="flex items-center text-gray-700">
                    <span class="bi bi-check text-green-500 ml-2"></span>
                    <span>سایدبار واکنش‌گرا</span>
                </li>
                <li class="flex items-center text-gray-700">
                    <span class="bi bi-check text-green-500 ml-2"></span>
                    <span>منوی تاشو</span>
                </li>
                <li class="flex items-center text-gray-700">
                    <span class="bi bi-check text-green-500 ml-2"></span>
                    <span>طراحی مدرن</span>
                </li>
                <li class="flex items-center text-gray-700">
                    <span class="bi bi-check text-green-500 ml-2"></span>
                    <span>انیمیشن‌های روان</span>
                </li>
            </ul>
        </div>
    </div>

    <!-- Bottom Section -->
    <div class="mt-6 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg shadow-md p-6 text-white">
        <div class="text-center">
            <h3 class="text-2xl font-bold mb-2">🎉 طراحی جدید آماده است!</h3>
            <p class="text-purple-100">حالا می‌توانید از Tailwind CSS برای طراحی صفحات خود استفاده کنید</p>
        </div>
    </div>
</div>
</AuthorizeView>

@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }
}
