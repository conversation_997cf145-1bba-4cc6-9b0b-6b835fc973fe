﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<!-- Mobile Menu Toggle -->
<input type="checkbox" id="mobile-menu-toggle" style="display: none;" />

<!-- Header Section -->
<div class="sidebar-header">
    <div style="display: flex; align-items: center; justify-content: space-between;">
        <a href="">مرکز زبان و مهارت‌های غالب</a>
        <!-- Mobile Menu Button -->
        <label for="mobile-menu-toggle" style="display: none; cursor: pointer; padding: 0.5rem;">
            <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </label>
    </div>
</div>

<!-- Navigation Menu -->
<div class="nav-menu" id="nav-menu">
    <nav style="padding: 0.5rem;">

       

        <!-- Home/Dashboard -->
        <div class="nav-item">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-graph-up nav-icon"></span>
                <span>داشبورد</span>
            </NavLink>
        </div>

        <!-- Enrollment Section -->
        <div class="nav-item">
            <button type="button" class="nav-toggle" @onclick="ToggleEnrollment">
                <div style="display: flex; align-items: center;">
                    <span class="bi bi-journal-plus nav-icon"></span>
                    <span>پذیرش</span>
                </div>
                <span style="transform: rotate(@(showEnrollment ? 90 : 0)deg); transition: transform 0.2s;">&#9656;</span>
            </button>

            @if (showEnrollment)
            {
                <div class="nav-submenu">
                    <NavLink class="nav-link" href="enroll" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-credit-card nav-icon"></span>
                        <span>ثبت نام</span>
                    </NavLink>

                    <NavLink class="nav-link" href="student" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-person-lines-fill nav-icon"></span>
                        <span>شاگردان</span>
                    </NavLink>

                    <NavLink class="nav-link" href="/home" Match="NavLinkMatch.All">
                        <span class="bi bi-house-door nav-icon"></span>
                        <span>تمام ثبت نام ها</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Finance Section -->
        <div class="nav-item">
            <button type="button" class="nav-toggle" @onclick="ToggleFinance">
                <div style="display: flex; align-items: center;">
                    <span class="bi bi-cash-stack nav-icon"></span>
                    <span>مالی</span>
                </div>
                <span style="transform: rotate(@(showFinance ? 90 : 0)deg); transition: transform 0.2s;">&#9656;</span>
            </button>

            @if (showFinance)
            {
                <div class="nav-submenu">
                    <NavLink class="nav-link" href="payment" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-credit-card nav-icon"></span>
                        <span>فیس</span>
                    </NavLink>

                    <NavLink class="nav-link" href="reports" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-journal-bookmark nav-icon"></span>
                        <span>گزارشات پرداختی</span>
                    </NavLink>

                    <NavLink class="nav-link" href="staffsalary" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-credit-card nav-icon"></span>
                        <span>معاش کارمندان</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Timetable -->
        <div class="nav-item">
            <NavLink class="nav-link" href="timetable">
                <span class="bi bi-clock nav-icon"></span>
                <span>تقسیم اوقات</span>
            </NavLink>
        </div>

        <!-- Staff Management -->
        <div class="nav-item">
            <NavLink class="nav-link" href="staff">
                <span class="bi bi-person-badge nav-icon"></span>
                <span>مدیریت کارمندان</span>
            </NavLink>
        </div>

        <!-- Settings -->
        <div class="nav-item">
            <NavLink class="nav-link" href="settings">
                <span class="bi bi-gear-fill nav-icon"></span>
                <span>تنظیمات</span>
            </NavLink>
        </div>

    </nav>
</div>



@code {
    private bool showEnrollment;
    private bool showFinance;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateMenuState(currentUrl);
    }

    private void ToggleEnrollment()
    {
        showEnrollment = !showEnrollment;
        showFinance = false;
    }

    private void ToggleFinance()
    {
        showFinance = !showFinance;
        showEnrollment = false;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateMenuState(currentUrl);
        StateHasChanged();
    }

    private void UpdateMenuState(string? url)
    {
        // Collapse all by default
        showEnrollment = false;
        showFinance = false;

        if (!string.IsNullOrEmpty(url))
        {
            if (url.StartsWith("student") || url.StartsWith("enroll"))
            {
                showEnrollment = true;
            }
            else if (url.StartsWith("payment") || url.StartsWith("reports"))
            {
                showFinance = true;
            }
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
