﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<!-- Mobile Menu Toggle -->
<input type="checkbox" id="mobile-menu-toggle" class="hidden" />

<!-- Modern Sidebar Container -->
<div class="h-full bg-white shadow-xl border-l border-gray-200 flex flex-col">

    <!-- Header Section with Logo -->
    <div class="p-6 border-b border-gray-100">
        <div class="flex items-center justify-between">
            <!-- Logo and Title -->
            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">غ</span>
                </div>
                <div class="hidden md:block">
                    <h1 class="text-lg font-bold text-gray-800 leading-tight">مرکز زبان و مهارت‌های</h1>
                    <p class="text-sm text-blue-600 font-medium">غالب</p>
                </div>
            </div>

            <!-- Mobile Menu Button -->
            <label for="mobile-menu-toggle" class="md:hidden p-2 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors duration-200">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </label>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto" id="nav-menu">

        <!-- Dashboard -->
        <NavLink class="group flex items-center px-4 py-3 text-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-blue-700 transition-all duration-300 transform hover:scale-105 hover:shadow-md" href="" Match="NavLinkMatch.All">
            <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg group-hover:from-blue-200 group-hover:to-purple-200 transition-all duration-300 ml-3">
                <span class="bi bi-speedometer2 text-blue-600 text-lg"></span>
            </div>
            <span class="font-medium">داشبورد</span>
        </NavLink>

        <!-- Enrollment Section -->
        <div class="space-y-1">
            <button type="button" class="group w-full flex items-center justify-between px-4 py-3 text-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-green-700 transition-all duration-300 transform hover:scale-105 hover:shadow-md" @onclick="ToggleEnrollment">
                <div class="flex items-center">
                    <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg group-hover:from-green-200 group-hover:to-emerald-200 transition-all duration-300 ml-3">
                        <span class="bi bi-person-plus text-green-600 text-lg"></span>
                    </div>
                    <span class="font-medium">پذیرش</span>
                </div>
                <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-300 @(showEnrollment ? "rotate-90" : "")" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            @if (showEnrollment)
            {
                <div class="mr-6 space-y-1 animate-fadeIn">
                    <NavLink class="group flex items-center px-4 py-2.5 text-gray-600 rounded-lg hover:bg-green-50 hover:text-green-700 transition-all duration-200 transform hover:translate-x-1" href="enroll" Match="NavLinkMatch.Prefix">
                        <div class="w-2 h-2 bg-green-300 rounded-full ml-4 group-hover:bg-green-500 transition-colors duration-200"></div>
                        <span class="text-sm font-medium">ثبت نام</span>
                    </NavLink>

                    <NavLink class="group flex items-center px-4 py-2.5 text-gray-600 rounded-lg hover:bg-green-50 hover:text-green-700 transition-all duration-200 transform hover:translate-x-1" href="student" Match="NavLinkMatch.Prefix">
                        <div class="w-2 h-2 bg-green-300 rounded-full ml-4 group-hover:bg-green-500 transition-colors duration-200"></div>
                        <span class="text-sm font-medium">شاگردان</span>
                    </NavLink>

                    <NavLink class="group flex items-center px-4 py-2.5 text-gray-600 rounded-lg hover:bg-green-50 hover:text-green-700 transition-all duration-200 transform hover:translate-x-1" href="/home" Match="NavLinkMatch.All">
                        <div class="w-2 h-2 bg-green-300 rounded-full ml-4 group-hover:bg-green-500 transition-colors duration-200"></div>
                        <span class="text-sm font-medium">تمام ثبت نام ها</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Finance Section -->
        <div class="space-y-1">
            <button type="button" class="group w-full flex items-center justify-between px-4 py-3 text-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-amber-50 hover:to-orange-50 hover:text-amber-700 transition-all duration-300 transform hover:scale-105 hover:shadow-md" @onclick="ToggleFinance">
                <div class="flex items-center">
                    <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-amber-100 to-orange-100 rounded-lg group-hover:from-amber-200 group-hover:to-orange-200 transition-all duration-300 ml-3">
                        <span class="bi bi-wallet2 text-amber-600 text-lg"></span>
                    </div>
                    <span class="font-medium">مالی</span>
                </div>
                <svg class="w-5 h-5 text-gray-400 transform transition-transform duration-300 @(showFinance ? "rotate-90" : "")" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            @if (showFinance)
            {
                <div class="mr-6 space-y-1 animate-fadeIn">
                    <NavLink class="group flex items-center px-4 py-2.5 text-gray-600 rounded-lg hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 transform hover:translate-x-1" href="payment" Match="NavLinkMatch.Prefix">
                        <div class="w-2 h-2 bg-amber-300 rounded-full ml-4 group-hover:bg-amber-500 transition-colors duration-200"></div>
                        <span class="text-sm font-medium">فیس</span>
                    </NavLink>

                    <NavLink class="group flex items-center px-4 py-2.5 text-gray-600 rounded-lg hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 transform hover:translate-x-1" href="reports" Match="NavLinkMatch.Prefix">
                        <div class="w-2 h-2 bg-amber-300 rounded-full ml-4 group-hover:bg-amber-500 transition-colors duration-200"></div>
                        <span class="text-sm font-medium">گزارشات پرداختی</span>
                    </NavLink>

                    <NavLink class="group flex items-center px-4 py-2.5 text-gray-600 rounded-lg hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 transform hover:translate-x-1" href="staffsalary" Match="NavLinkMatch.Prefix">
                        <div class="w-2 h-2 bg-amber-300 rounded-full ml-4 group-hover:bg-amber-500 transition-colors duration-200"></div>
                        <span class="text-sm font-medium">معاش کارمندان</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Timetable -->
        <NavLink class="group flex items-center px-4 py-3 text-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-300 transform hover:scale-105 hover:shadow-md" href="timetable">
            <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-lg group-hover:from-indigo-200 group-hover:to-purple-200 transition-all duration-300 ml-3">
                <span class="bi bi-calendar3 text-indigo-600 text-lg"></span>
            </div>
            <span class="font-medium">تقسیم اوقات</span>
        </NavLink>

        <!-- Staff Management -->
        <NavLink class="group flex items-center px-4 py-3 text-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-rose-50 hover:to-pink-50 hover:text-rose-700 transition-all duration-300 transform hover:scale-105 hover:shadow-md" href="staff">
            <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-rose-100 to-pink-100 rounded-lg group-hover:from-rose-200 group-hover:to-pink-200 transition-all duration-300 ml-3">
                <span class="bi bi-people text-rose-600 text-lg"></span>
            </div>
            <span class="font-medium">مدیریت کارمندان</span>
        </NavLink>

        <!-- Settings -->
        <NavLink class="group flex items-center px-4 py-3 text-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-700 transition-all duration-300 transform hover:scale-105 hover:shadow-md" href="settings">
            <div class="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-gray-100 to-slate-100 rounded-lg group-hover:from-gray-200 group-hover:to-slate-200 transition-all duration-300 ml-3">
                <span class="bi bi-gear text-gray-600 text-lg"></span>
            </div>
            <span class="font-medium">تنظیمات</span>
        </NavLink>

    </nav>

    <!-- Footer Section -->
    <div class="p-4 border-t border-gray-100 bg-gray-50">
        <div class="flex items-center justify-center space-x-2 rtl:space-x-reverse">
            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">غ</span>
            </div>
            <div class="text-center">
                <p class="text-xs text-gray-500 font-medium">نسخه 2.0</p>
                <p class="text-xs text-gray-400">طراحی مدرن</p>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Menu Styles -->
<style>

</style>



@code {
    private bool showEnrollment;
    private bool showFinance;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateMenuState(currentUrl);
    }

    private void ToggleEnrollment()
    {
        showEnrollment = !showEnrollment;
        showFinance = false;
    }

    private void ToggleFinance()
    {
        showFinance = !showFinance;
        showEnrollment = false;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateMenuState(currentUrl);
        StateHasChanged();
    }

    private void UpdateMenuState(string? url)
    {
        // Collapse all by default
        showEnrollment = false;
        showFinance = false;

        if (!string.IsNullOrEmpty(url))
        {
            if (url.StartsWith("student") || url.StartsWith("enroll"))
            {
                showEnrollment = true;
            }
            else if (url.StartsWith("payment") || url.StartsWith("reports"))
            {
                showFinance = true;
            }
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
