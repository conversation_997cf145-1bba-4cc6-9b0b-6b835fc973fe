﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<!-- Mobile Menu Toggle -->
<input type="checkbox" id="mobile-menu-toggle" class="hidden" />

<!-- Modern Sidebar Container -->
<div class="modern-sidebar">

    <!-- Header Section with Logo -->
    <div class="sidebar-header">
        <div class="logo-container">
            <!-- Logo and Title -->
            <div style="display: flex; align-items: center;">
                <div class="logo-badge">
                    <span>غ</span>
                </div>
                <div class="logo-text" style="display: none;">
                    <h1 class="logo-title">مرکز زبان و مهارت‌های</h1>
                    <p class="logo-subtitle">غالب</p>
                </div>
            </div>

            <!-- Mobile Menu Button -->
            <label for="mobile-menu-toggle" style="display: none; padding: 0.5rem; border-radius: 0.5rem; cursor: pointer; transition: background-color 0.2s;">
                <svg style="width: 1.5rem; height: 1.5rem; color: rgb(75, 85, 99);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </label>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="nav-container" id="nav-menu">

        <style>
          
        </style>

        <!-- Dashboard -->
        <div class="nav-item">
            <NavLink class="nav-link nav-dashboard" href="" Match="NavLinkMatch.All">
                <div class="nav-icon-container icon-dashboard">
                    <span class="bi bi-speedometer2 nav-icon"></span>
                </div>
                <span class="nav-text">داشبورد</span>
            </NavLink>
        </div>

        <!-- Enrollment Section -->
        <div class="nav-item">
            <button type="button" class="nav-toggle nav-enrollment" @onclick="ToggleEnrollment">
                <div style="display: flex; align-items: center;">
                    <div class="nav-icon-container icon-enrollment">
                        <span class="bi bi-person-plus nav-icon" style="color: #059669;"></span>
                    </div>
                    <span class="nav-text">پذیرش</span>
                </div>
                <svg style="width: 1.25rem; height: 1.25rem; color: rgb(156, 163, 175); transform: rotate(@(showEnrollment ? 90 : 0)deg); transition: transform 0.3s;" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            @if (showEnrollment)
            {
                <div class="nav-submenu">
                    <NavLink class="nav-link" href="enroll" Match="NavLinkMatch.Prefix">
                        <div class="submenu-dot" style="background-color: #86efac;"></div>
                        <span class="nav-text">ثبت نام</span>
                    </NavLink>

                    <NavLink class="nav-link" href="student" Match="NavLinkMatch.Prefix">
                        <div class="submenu-dot" style="background-color: #86efac;"></div>
                        <span class="nav-text">شاگردان</span>
                    </NavLink>

                    <NavLink class="nav-link" href="/home" Match="NavLinkMatch.All">
                        <div class="submenu-dot" style="background-color: #86efac;"></div>
                        <span class="nav-text">تمام ثبت نام ها</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Finance Section -->
        <div class="nav-item">
            <button type="button" class="nav-toggle nav-finance" @onclick="ToggleFinance">
                <div style="display: flex; align-items: center;">
                    <div class="nav-icon-container icon-finance">
                        <span class="bi bi-wallet2 nav-icon" style="color: #d97706;"></span>
                    </div>
                    <span class="nav-text">مالی</span>
                </div>
                <svg style="width: 1.25rem; height: 1.25rem; color: rgb(156, 163, 175); transform: rotate(@(showFinance ? 90 : 0)deg); transition: transform 0.3s;" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            @if (showFinance)
            {
                <div class="nav-submenu">
                    <NavLink class="nav-link" href="payment" Match="NavLinkMatch.Prefix">
                        <div class="submenu-dot" style="background-color: #fcd34d;"></div>
                        <span class="nav-text">فیس</span>
                    </NavLink>

                    <NavLink class="nav-link" href="reports" Match="NavLinkMatch.Prefix">
                        <div class="submenu-dot" style="background-color: #fcd34d;"></div>
                        <span class="nav-text">گزارشات پرداختی</span>
                    </NavLink>

                    <NavLink class="nav-link" href="staffsalary" Match="NavLinkMatch.Prefix">
                        <div class="submenu-dot" style="background-color: #fcd34d;"></div>
                        <span class="nav-text">معاش کارمندان</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Timetable -->
        <div class="nav-item">
            <NavLink class="nav-link nav-timetable" href="timetable">
                <div class="nav-icon-container icon-timetable">
                    <span class="bi bi-calendar3 nav-icon" style="color: #4338ca;"></span>
                </div>
                <span class="nav-text">تقسیم اوقات</span>
            </NavLink>
        </div>

        <!-- Staff Management -->
        <div class="nav-item">
            <NavLink class="nav-link nav-staff" href="staff">
                <div class="nav-icon-container icon-staff">
                    <span class="bi bi-people nav-icon" style="color: #be185d;"></span>
                </div>
                <span class="nav-text">مدیریت کارمندان</span>
            </NavLink>
        </div>

        <!-- Settings -->
        <div class="nav-item">
            <NavLink class="nav-link nav-settings" href="settings">
                <div class="nav-icon-container icon-settings">
                    <span class="bi bi-gear nav-icon" style="color: rgb(75, 85, 99);"></span>
                </div>
                <span class="nav-text">تنظیمات</span>
            </NavLink>
        </div>

    </nav>

    <!-- Footer Section -->
    <div class="sidebar-footer">
        <div class="footer-content">
            <div class="footer-logo">
                <span style="color: white; font-weight: bold; font-size: 0.875rem;">غ</span>
            </div>
            <div class="footer-text">
                <p class="version-text">نسخه 2.0</p>
                <p class="design-text">طراحی مدرن</p>
            </div>
        </div>
    </div>
</div>





@code {
    private bool showEnrollment;
    private bool showFinance;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateMenuState(currentUrl);
    }

    private void ToggleEnrollment()
    {
        showEnrollment = !showEnrollment;
        showFinance = false;
    }

    private void ToggleFinance()
    {
        showFinance = !showFinance;
        showEnrollment = false;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateMenuState(currentUrl);
        StateHasChanged();
    }

    private void UpdateMenuState(string? url)
    {
        // Collapse all by default
        showEnrollment = false;
        showFinance = false;

        if (!string.IsNullOrEmpty(url))
        {
            if (url.StartsWith("student") || url.StartsWith("enroll"))
            {
                showEnrollment = true;
            }
            else if (url.StartsWith("payment") || url.StartsWith("reports"))
            {
                showFinance = true;
            }
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
