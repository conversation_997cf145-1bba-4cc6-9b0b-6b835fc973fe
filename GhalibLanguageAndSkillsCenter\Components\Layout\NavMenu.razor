﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<!-- Mobile Menu Toggle -->
<input type="checkbox" id="mobile-menu-toggle" class="hidden" />

<!-- Header Section -->
<div class="bg-blue-800 text-white p-3 md:p-4">
    <div class="flex items-center justify-between">
        <a class="text-lg md:text-xl font-bold text-white hover:text-blue-200 transition-colors duration-200" href="">
            مرکز زبان و مهارت‌های غالب
        </a>
        <!-- Mobile Menu Button -->
        <label for="mobile-menu-toggle" class="md:hidden cursor-pointer p-2 hover:bg-blue-700 rounded transition-colors duration-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </label>
    </div>
</div>

<!-- Navigation Menu -->
<div class="hidden md:block md:h-[calc(100vh-4rem)] md:overflow-y-auto" id="nav-menu">
    <nav class="p-2 space-y-1">

        <!-- Home/Dashboard -->
        <div class="w-full">
            <NavLink class="flex items-center w-full px-3 py-3 text-gray-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 group" href="" Match="NavLinkMatch.All">
                <span class="bi bi-graph-up ml-3 text-lg group-hover:scale-110 transition-transform duration-200"></span>
                <span class="font-medium">داشبورد</span>
            </NavLink>
        </div>

        <!-- Enrollment Section -->
        <div class="w-full">
            <button type="button" class="flex items-center justify-between w-full px-3 py-3 text-gray-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 group" @onclick="ToggleEnrollment">
                <div class="flex items-center">
                    <span class="bi bi-journal-plus ml-3 text-lg group-hover:scale-110 transition-transform duration-200"></span>
                    <span class="font-medium">پذیرش</span>
                </div>
                <svg class="w-5 h-5 transform transition-transform duration-200 @(showEnrollment ? "rotate-90" : "")" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            @if (showEnrollment)
            {
                <div class="mr-6 mt-2 space-y-1">
                    <NavLink class="flex items-center w-full px-3 py-2 text-gray-300 hover:bg-white/5 hover:text-white rounded-md transition-all duration-200 text-sm" href="enroll" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-credit-card ml-3"></span>
                        <span>ثبت نام</span>
                    </NavLink>

                    <NavLink class="flex items-center w-full px-3 py-2 text-gray-300 hover:bg-white/5 hover:text-white rounded-md transition-all duration-200 text-sm" href="student" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-person-lines-fill ml-3"></span>
                        <span>شاگردان</span>
                    </NavLink>

                    <NavLink class="flex items-center w-full px-3 py-2 text-gray-300 hover:bg-white/5 hover:text-white rounded-md transition-all duration-200 text-sm" href="/home" Match="NavLinkMatch.All">
                        <span class="bi bi-house-door ml-3"></span>
                        <span>تمام ثبت نام ها</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Finance Section -->
        <div class="w-full">
            <button type="button" class="flex items-center justify-between w-full px-3 py-3 text-gray-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 group" @onclick="ToggleFinance">
                <div class="flex items-center">
                    <span class="bi bi-cash-stack ml-3 text-lg group-hover:scale-110 transition-transform duration-200"></span>
                    <span class="font-medium">مالی</span>
                </div>
                <svg class="w-5 h-5 transform transition-transform duration-200 @(showFinance ? "rotate-90" : "")" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>

            @if (showFinance)
            {
                <div class="mr-6 mt-2 space-y-1">
                    <NavLink class="flex items-center w-full px-3 py-2 text-gray-300 hover:bg-white/5 hover:text-white rounded-md transition-all duration-200 text-sm" href="payment" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-credit-card ml-3"></span>
                        <span>فیس</span>
                    </NavLink>

                    <NavLink class="flex items-center w-full px-3 py-2 text-gray-300 hover:bg-white/5 hover:text-white rounded-md transition-all duration-200 text-sm" href="reports" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-journal-bookmark ml-3"></span>
                        <span>گزارشات پرداختی</span>
                    </NavLink>

                    <NavLink class="flex items-center w-full px-3 py-2 text-gray-300 hover:bg-white/5 hover:text-white rounded-md transition-all duration-200 text-sm" href="staffsalary" Match="NavLinkMatch.Prefix">
                        <span class="bi bi-credit-card ml-3"></span>
                        <span>معاش کارمندان</span>
                    </NavLink>
                </div>
            }
        </div>

        <!-- Timetable -->
        <div class="w-full">
            <NavLink class="flex items-center w-full px-3 py-3 text-gray-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 group" href="timetable">
                <span class="bi bi-clock ml-3 text-lg group-hover:scale-110 transition-transform duration-200"></span>
                <span class="font-medium">تقسیم اوقات</span>
            </NavLink>
        </div>

        <!-- Staff Management -->
        <div class="w-full">
            <NavLink class="flex items-center w-full px-3 py-3 text-gray-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 group" href="staff">
                <span class="bi bi-person-badge ml-3 text-lg group-hover:scale-110 transition-transform duration-200"></span>
                <span class="font-medium">مدیریت کارمندان</span>
            </NavLink>
        </div>

        <!-- Settings -->
        <div class="w-full">
            <NavLink class="flex items-center w-full px-3 py-3 text-gray-200 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-200 group" href="settings">
                <span class="bi bi-gear-fill ml-3 text-lg group-hover:scale-110 transition-transform duration-200"></span>
                <span class="font-medium">تنظیمات</span>
            </NavLink>
        </div>

    </nav>
</div>



@code {
    private bool showEnrollment;
    private bool showFinance;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateMenuState(currentUrl);
    }

    private void ToggleEnrollment()
    {
        showEnrollment = !showEnrollment;
        showFinance = false;
    }

    private void ToggleFinance()
    {
        showFinance = !showFinance;
        showEnrollment = false;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateMenuState(currentUrl);
        StateHasChanged();
    }

    private void UpdateMenuState(string? url)
    {
        // Collapse all by default
        showEnrollment = false;
        showFinance = false;

        if (!string.IsNullOrEmpty(url))
        {
            if (url.StartsWith("student") || url.StartsWith("enroll"))
            {
                showEnrollment = true;
            }
            else if (url.StartsWith("payment") || url.StartsWith("reports"))
            {
                showFinance = true;
            }
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
