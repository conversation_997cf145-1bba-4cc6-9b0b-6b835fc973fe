@tailwind base;
@tailwind components;
@tailwind utilities;

/* Override Bootstrap styles for layout */
@layer utilities {
  .layout-container {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
  }

  @media (min-width: 768px) {
    .layout-container {
      flex-direction: row !important;
    }
  }

  .sidebar-container {
    width: 100% !important;
    background: linear-gradient(to bottom, rgb(30, 58, 138), rgb(88, 28, 135)) !important;
  }

  @media (min-width: 768px) {
    .sidebar-container {
      width: 16rem !important;
      height: 100vh !important;
      position: sticky !important;
      top: 0 !important;
    }
  }

  .main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .top-header {
    background-color: rgb(243, 244, 246) !important;
    border-bottom: 1px solid rgb(209, 213, 219) !important;
    height: 3.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0 1rem !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }

  .page-content {
    flex: 1 !important;
    padding: 1rem !important;
    background-color: rgb(249, 250, 251) !important;
  }
}

/* Navigation Styles */
@layer components {
  /* Sidebar header */
  .sidebar-header {
    background-color: rgb(30, 64, 175) !important;
    color: white !important;
    padding: 1rem !important;
  }

  .sidebar-header a {
    color: white !important;
    text-decoration: none !important;
    font-size: 1.25rem !important;
    font-weight: bold !important;
  }

  .sidebar-header a:hover {
    color: rgb(191, 219, 254) !important;
  }

  /* Navigation menu */
  .nav-menu {
    padding: 0.5rem !important;
  }

  .nav-item {
    width: 100% !important;
    margin-bottom: 0.25rem !important;
  }

  .nav-link {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 0.75rem !important;
    color: rgb(229, 231, 235) !important;
    text-decoration: none !important;
    border-radius: 0.5rem !important;
    transition: all 0.2s !important;
    direction: rtl !important;
  }

  .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    text-decoration: none !important;
  }

  .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    font-weight: 600 !important;
  }

  .nav-icon {
    display: inline-block !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    margin-left: 0.75rem !important;
    font-size: 1.125rem !important;
  }

  /* Collapsible sections */
  .nav-toggle {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
    padding: 0.75rem !important;
    color: rgb(229, 231, 235) !important;
    background: none !important;
    border: none !important;
    border-radius: 0.5rem !important;
    transition: all 0.2s !important;
    direction: rtl !important;
    cursor: pointer !important;
  }

  .nav-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
  }

  .nav-submenu {
    margin-right: 1.5rem !important;
    margin-top: 0.5rem !important;
  }

  .nav-submenu .nav-link {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
    color: rgb(209, 213, 219) !important;
  }

  .nav-submenu .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: white !important;
  }

  /* Mobile menu */
  #mobile-menu-toggle:checked ~ .nav-menu {
    display: block !important;
  }

  @media (max-width: 767px) {
    .nav-menu {
      display: none !important;
      position: absolute !important;
      top: 100% !important;
      left: 0 !important;
      right: 0 !important;
      background: linear-gradient(to bottom, rgb(30, 58, 138), rgb(88, 28, 135)) !important;
      z-index: 50 !important;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    }
  }
}
