@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Navigation Styles */
@layer components {
  /* Active navigation link styles */
  .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
  }

  /* Mobile menu toggle functionality */
  #mobile-menu-toggle:checked ~ #nav-menu {
    display: block !important;
  }

  /* Ensure proper RTL support for navigation */
  .nav-link {
    direction: rtl;
  }
}

/* Custom styles for better RTL support and navigation */
@layer utilities {
  .nav-icon {
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: 0.75rem;
  }
}
