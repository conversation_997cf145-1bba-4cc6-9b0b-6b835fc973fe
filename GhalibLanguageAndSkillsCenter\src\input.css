@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force include commonly used classes */
@layer utilities {
  .from-blue-500 { --tw-gradient-from: #3b82f6; }
  .to-purple-600 { --tw-gradient-to: #9333ea; }
  .from-green-100 { --tw-gradient-from: #dcfce7; }
  .to-emerald-100 { --tw-gradient-to: #d1fae5; }
  .from-amber-100 { --tw-gradient-from: #fef3c7; }
  .to-orange-100 { --tw-gradient-to: #fed7aa; }
  .from-indigo-100 { --tw-gradient-from: #e0e7ff; }
  .to-purple-100 { --tw-gradient-to: #f3e8ff; }
  .from-rose-100 { --tw-gradient-from: #ffe4e6; }
  .to-pink-100 { --tw-gradient-to: #fce7f3; }
  .from-gray-100 { --tw-gradient-from: #f3f4f6; }
  .to-slate-100 { --tw-gradient-to: #f1f5f9; }

  .hover\:scale-105:hover { transform: scale(1.05); }
  .rounded-xl { border-radius: 0.75rem; }
  .rounded-lg { border-radius: 0.5rem; }
  .shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
  .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
}

/* Override Bootstrap styles for layout */
@layer utilities {
  .layout-container {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
  }

  @media (min-width: 768px) {
    .layout-container {
      flex-direction: row !important;
    }
  }

  .sidebar-container {
    width: 100% !important;
    background: white !important;
    border-left: 1px solid rgb(229, 231, 235) !important;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1) !important;
  }

  @media (min-width: 768px) {
    .sidebar-container {
      width: 16rem !important;
      height: 100vh !important;
      position: sticky !important;
      top: 0 !important;
    }
  }

  .main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .top-header {
    background-color: rgb(243, 244, 246) !important;
    border-bottom: 1px solid rgb(209, 213, 219) !important;
    height: 3.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0 1rem !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }

  .page-content {
    flex: 1 !important;
    padding: 1rem !important;
    background-color: rgb(249, 250, 251) !important;
  }
}

/* Complete Modern Sidebar Styles */
@layer components {
  /* Sidebar container */
  .modern-sidebar {
    height: 100%;
    background: white;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-left: 1px solid rgb(229, 231, 235);
    display: flex;
    flex-direction: column;
  }

  /* Header section */
  .sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgb(243, 244, 246);
  }

  .logo-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .logo-badge {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    color: white;
    font-weight: bold;
    font-size: 1.125rem;
  }

  .logo-text {
    margin-right: 0.75rem;
  }

  .logo-title {
    font-size: 1.125rem;
    font-weight: bold;
    color: rgb(31, 41, 55);
    line-height: 1.25;
  }

  .logo-subtitle {
    font-size: 0.875rem;
    color: #3b82f6;
    font-weight: 500;
  }

  /* Navigation */
  .nav-container {
    flex: 1;
    padding: 1rem 1rem 1.5rem;
    overflow-y: auto;
  }

  .nav-item {
    margin-bottom: 0.5rem;
  }

  .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgb(55, 65, 81);
    text-decoration: none;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    transform: scale(1);
  }

  .nav-link:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    text-decoration: none;
  }

  /* Dashboard */
  .nav-dashboard:hover {
    background: linear-gradient(135deg, rgb(239, 246, 255), rgb(243, 232, 255));
    color: #1d4ed8;
  }

  /* Enrollment */
  .nav-enrollment:hover {
    background: linear-gradient(135deg, rgb(236, 253, 245), rgb(209, 250, 229));
    color: #059669;
  }

  /* Finance */
  .nav-finance:hover {
    background: linear-gradient(135deg, rgb(255, 251, 235), rgb(254, 243, 199));
    color: #d97706;
  }

  /* Timetable */
  .nav-timetable:hover {
    background: linear-gradient(135deg, rgb(238, 242, 255), rgb(243, 232, 255));
    color: #4338ca;
  }

  /* Staff */
  .nav-staff:hover {
    background: linear-gradient(135deg, rgb(255, 228, 230), rgb(252, 231, 243));
    color: #be185d;
  }

  /* Settings */
  .nav-settings:hover {
    background: linear-gradient(135deg, rgb(249, 250, 251), rgb(241, 245, 249));
    color: rgb(55, 65, 81);
  }

  .nav-icon-container {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    transition: all 0.3s ease;
  }

  /* Icon backgrounds */
  .icon-dashboard {
    background: linear-gradient(135deg, rgb(219, 234, 254), rgb(221, 214, 254));
  }

  .icon-enrollment {
    background: linear-gradient(135deg, rgb(220, 252, 231), rgb(167, 243, 208));
  }

  .icon-finance {
    background: linear-gradient(135deg, rgb(254, 243, 199), rgb(253, 230, 138));
  }

  .icon-timetable {
    background: linear-gradient(135deg, rgb(224, 231, 255), rgb(243, 232, 255));
  }

  .icon-staff {
    background: linear-gradient(135deg, rgb(254, 226, 226), rgb(252, 231, 243));
  }

  .icon-settings {
    background: linear-gradient(135deg, rgb(243, 244, 246), rgb(241, 245, 249));
  }

  .nav-icon {
    font-size: 1.125rem;
    color: #3b82f6;
  }

  .nav-text {
    font-weight: 500;
  }

  /* Toggle button */
  .nav-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    color: rgb(55, 65, 81);
    background: none;
    border: none;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    cursor: pointer;
    transform: scale(1);
  }

  .nav-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Submenu */
  .nav-submenu {
    margin-right: 1.5rem;
    margin-top: 0.5rem;
  }

  .nav-submenu .nav-link {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    color: rgb(107, 114, 128);
  }

  .submenu-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    margin-left: 1rem;
    transition: all 0.2s ease;
  }

  /* Active states */
  .nav-link.active {
    background: linear-gradient(135deg, #3b82f6, #9333ea) !important;
    color: white !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.5) !important;
  }

  .nav-link.active .nav-icon-container {
    background: rgba(255, 255, 255, 0.2) !important;
  }

  .nav-link.active .nav-icon {
    color: white !important;
  }

  /* Footer */
  .sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgb(243, 244, 246);
    background: rgb(249, 250, 251);
  }

  .footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .footer-logo {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
  }

  .footer-text {
    text-align: center;
  }

  .version-text {
    font-size: 0.75rem;
    color: rgb(107, 114, 128);
    font-weight: 500;
  }

  .design-text {
    font-size: 0.75rem;
    color: rgb(156, 163, 175);
  }
}
