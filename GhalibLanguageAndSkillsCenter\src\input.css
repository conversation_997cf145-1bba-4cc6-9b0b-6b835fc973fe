@tailwind base;
@tailwind components;
@tailwind utilities;

/* Override Bootstrap styles for layout */
@layer utilities {
  .layout-container {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
  }

  @media (min-width: 768px) {
    .layout-container {
      flex-direction: row !important;
    }
  }

  .sidebar-container {
    width: 100% !important;
    background: white !important;
    border-left: 1px solid rgb(229, 231, 235) !important;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1) !important;
  }

  @media (min-width: 768px) {
    .sidebar-container {
      width: 16rem !important;
      height: 100vh !important;
      position: sticky !important;
      top: 0 !important;
    }
  }

  .main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .top-header {
    background-color: rgb(243, 244, 246) !important;
    border-bottom: 1px solid rgb(209, 213, 219) !important;
    height: 3.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0 1rem !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }

  .page-content {
    flex: 1 !important;
    padding: 1rem !important;
    background-color: rgb(249, 250, 251) !important;
  }
}

/* Modern Navigation Styles */
@layer components {
  /* Active navigation states */
  .nav-link.active {
    background: linear-gradient(135deg, rgb(59, 130, 246), rgb(147, 51, 234)) !important;
    color: white !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.5) !important;
  }

  .nav-link.active .bg-gradient-to-br {
    background: rgba(255, 255, 255, 0.2) !important;
  }

  .nav-link.active span {
    color: white !important;
  }

  /* Submenu active states */
  .nav-submenu .nav-link.active {
    background: linear-gradient(135deg, rgb(34, 197, 94), rgb(59, 130, 246)) !important;
    color: white !important;
    transform: translateX(4px) !important;
    box-shadow: 0 4px 15px -4px rgba(34, 197, 94, 0.4) !important;
  }

  .nav-submenu .nav-link.active .w-2 {
    background-color: white !important;
  }
}
