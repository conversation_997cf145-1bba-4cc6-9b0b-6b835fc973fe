/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
.collapse {
  visibility: collapse;
}
.invisible {
  visibility: hidden;
}
.visible {
  visibility: visible;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.relative {
  position: relative;
}
.static {
  position: static;
}
.sticky {
  position: sticky;
}
.isolate {
  isolation: isolate;
}
.z-0 {
  z-index: 0;
}
.z-1 {
  z-index: 1;
}
.z-2 {
  z-index: 2;
}
.z-3 {
  z-index: 3;
}
.z-50 {
  z-index: 50;
}
.order-0 {
  order: 0;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.order-3 {
  order: 3;
}
.order-4 {
  order: 4;
}
.order-5 {
  order: 5;
}
.order-first {
  order: -9999;
}
.order-last {
  order: 9999;
}
.col-1 {
  grid-column: 1;
}
.col-2 {
  grid-column: 2;
}
.col-3 {
  grid-column: 3;
}
.col-4 {
  grid-column: 4;
}
.col-5 {
  grid-column: 5;
}
.col-6 {
  grid-column: 6;
}
.col-7 {
  grid-column: 7;
}
.col-8 {
  grid-column: 8;
}
.col-9 {
  grid-column: 9;
}
.col-10 {
  grid-column: 10;
}
.col-11 {
  grid-column: 11;
}
.col-12 {
  grid-column: 12;
}
.col-auto {
  grid-column: auto;
}
.float-end {
  float: inline-end;
}
.float-none {
  float: none;
}
.float-start {
  float: inline-start;
}
.container {
  width: 100%;
}
.m-auto {
  margin: auto;
}
.mx-auto {
  margin-inline: auto;
}
.my-auto {
  margin-block: auto;
}
.ms-auto {
  margin-inline-start: auto;
}
.me-auto {
  margin-inline-end: auto;
}
.mt-auto {
  margin-top: auto;
}
.mb-auto {
  margin-bottom: auto;
}
.block {
  display: block;
}
.contents {
  display: contents;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.inline-flex {
  display: inline-flex;
}
.inline-grid {
  display: inline-grid;
}
.table {
  display: table;
}
.table-cell {
  display: table-cell;
}
.table-row {
  display: table-row;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.flex-1 {
  flex: 1;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-shrink-1 {
  flex-shrink: 1;
}
.shrink {
  flex-shrink: 1;
}
.flex-grow {
  flex-grow: 1;
}
.flex-grow-0 {
  flex-grow: 0;
}
.flex-grow-1 {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.caption-top {
  caption-side: top;
}
.border-collapse {
  border-collapse: collapse;
}
.rotate-90 {
  rotate: 90deg;
}
.transform {
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-text {
  cursor: text;
}
.resize {
  resize: both;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.items-center {
  align-items: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-scroll {
  overflow: scroll;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-x-scroll {
  overflow-x: scroll;
}
.overflow-x-visible {
  overflow-x: visible;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.overflow-y-visible {
  overflow-y: visible;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-0 {
  border-style: var(--tw-border-style);
  border-width: 0px;
}
.border-1 {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}
.border-3 {
  border-style: var(--tw-border-style);
  border-width: 3px;
}
.border-4 {
  border-style: var(--tw-border-style);
  border-width: 4px;
}
.border-5 {
  border-style: var(--tw-border-style);
  border-width: 5px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-t-2 {
  border-top-style: var(--tw-border-style);
  border-top-width: 2px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-l {
  border-left-style: var(--tw-border-style);
  border-left-width: 1px;
}
.bg-transparent {
  background-color: transparent;
}
.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}
.text-center {
  text-align: center;
}
.text-end {
  text-align: end;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-start {
  text-align: start;
}
.align-baseline {
  vertical-align: baseline;
}
.align-bottom {
  vertical-align: bottom;
}
.align-middle {
  vertical-align: middle;
}
.align-text-bottom {
  vertical-align: text-bottom;
}
.align-text-top {
  vertical-align: text-top;
}
.align-top {
  vertical-align: top;
}
.text-nowrap {
  text-wrap: nowrap;
}
.text-wrap {
  text-wrap: wrap;
}
.capitalize {
  text-transform: capitalize;
}
.lowercase {
  text-transform: lowercase;
}
.uppercase {
  text-transform: uppercase;
}
.italic {
  font-style: italic;
}
.line-through {
  text-decoration-line: line-through;
}
.underline {
  text-decoration-line: underline;
}
.opacity-0 {
  opacity: 0%;
}
.opacity-25 {
  opacity: 25%;
}
.opacity-50 {
  opacity: 50%;
}
.opacity-75 {
  opacity: 75%;
}
.opacity-100 {
  opacity: 100%;
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.ring {
  --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.filter {
  filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
}
.transition {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}
.duration-200 {
  --tw-duration: 200ms;
  transition-duration: 200ms;
}
.duration-300 {
  --tw-duration: 300ms;
  transition-duration: 300ms;
}
.group-hover\:scale-110 {
  &:is(:where(.group):hover *) {
    @media (hover: hover) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.hover\:scale-105 {
  &:hover {
    @media (hover: hover) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.hover\:scale-110 {
  &:hover {
    @media (hover: hover) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
}
.hover\:bg-gradient-to-r {
  &:hover {
    @media (hover: hover) {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }
}
.hover\:underline {
  &:hover {
    @media (hover: hover) {
      text-decoration-line: underline;
    }
  }
}
.rtl\:space-x-reverse {
  &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 1;
    }
  }
}
@layer utilities {
  .layout-container {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
  }
  @media (min-width: 768px) {
    .layout-container {
      flex-direction: row !important;
    }
  }
  .sidebar-container {
    width: 100% !important;
    background: white !important;
    border-left: 1px solid rgb(229, 231, 235) !important;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1) !important;
  }
  @media (min-width: 768px) {
    .sidebar-container {
      width: 16rem !important;
      height: 100vh !important;
      position: sticky !important;
      top: 0 !important;
    }
  }
  .main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }
  .top-header {
    background-color: rgb(243, 244, 246) !important;
    border-bottom: 1px solid rgb(209, 213, 219) !important;
    height: 3.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0 1rem !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 10 !important;
  }
  .page-content {
    flex: 1 !important;
    padding: 1rem !important;
    background-color: rgb(249, 250, 251) !important;
  }
}
@layer components {
  .nav-link.active {
    background: linear-gradient(135deg, rgb(59, 130, 246), rgb(147, 51, 234)) !important;
    color: white !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.5) !important;
  }
  .nav-link.active .bg-gradient-to-br {
    background: rgba(255, 255, 255, 0.2) !important;
  }
  .nav-link.active span {
    color: white !important;
  }
  .nav-submenu .nav-link.active {
    background: linear-gradient(135deg, rgb(34, 197, 94), rgb(59, 130, 246)) !important;
    color: white !important;
    transform: translateX(4px) !important;
    box-shadow: 0 4px 15px -4px rgba(34, 197, 94, 0.4) !important;
  }
  .nav-submenu .nav-link.active .w-2 {
    background-color: white !important;
  }
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-space-x-reverse: 0;
    }
  }
}
