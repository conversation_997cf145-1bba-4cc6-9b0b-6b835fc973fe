﻿@inherits LayoutComponentBase

@using Microsoft.AspNetCore.Components.Authorization

<!DOCTYPE html>
<html lang="fa" dir="rtl">
 <!-- Set language to Persian and direction to RTL -->
<head>
    <!-- Add meta tags and other head content if needed -->
    <style>
        /* Fallback styles to ensure sidebar is visible */
        .layout-container {
            display: flex !important;
            flex-direction: column !important;
            min-height: 100vh !important;
        }

        .sidebar-container {
            width: 100% !important;
            background: linear-gradient(to bottom, rgb(30, 58, 138), rgb(88, 28, 135)) !important;
            color: white !important;
        }

        .main-content {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
        }

     
        }
    </style>
</head>

<body>

    <!-- Main Layout Container -->
    <div class="layout-container relative">
        <!-- Sidebar -->
        <div class="sidebar-container" dir="rtl">
            <NavMenu />
        </div>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Header Bar -->
            <div class="top-header">

                <!-- AuthorizeView to show either "Hello, user!" or the About link -->
                <AuthorizeView Context="authState">
                    <Authorized>
                        <span class="px-3 text-gray-700 font-medium">
                            سلام، @authState.User.Identity.Name!
                        </span>
                    </Authorized>
                </AuthorizeView>

                <AuthorizeView Roles="Administrator">
                    <NavLink class="flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200" href="Account/Register">
                        <span class="bi bi-person-plus-fill ml-2" aria-hidden="true"></span> ایجاد یوزر
                    </NavLink>
                </AuthorizeView>

                <!-- Login / Logout -->
                <AuthorizeView>
                    <Authorized>
                        <div class="ml-3">
                            <NavLink class="flex items-center px-3 py-2 text-red-600 hover:text-red-800 hover:underline transition-colors duration-200" href="Account/Logout">
                                <span class="bi bi-box-arrow-right ml-2" aria-hidden="true"></span>
                                خروج
                            </NavLink>
                        </div>
                    </Authorized>
                    <NotAuthorized>
                        <div class="px-3">
                            <NavLink class="flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200" href="Account/Login">
                                <span class="bi bi-person-badge-nav-menu ml-2" aria-hidden="true"></span>
                                ورود
                            </NavLink>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>

            </div>

            <!-- Snackbar Component -->
			<TestSnackbar />

            <!-- Page Content -->
            <article class="page-content">
                @Body
            </article>

        </main>
    </div>

    <!-- Error UI -->
    <div id="blazor-error-ui" class="hidden fixed bottom-0 left-0 w-full bg-yellow-100 border-t-2 border-yellow-300 p-3 z-50" data-nosnippet>
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <span class="text-yellow-800">یک خطای ناشناخته رخ داده است.</span>
            <div class="flex items-center space-x-4">
                <a href="." class="text-yellow-800 hover:text-yellow-900 underline">بارگذاری مجدد</a>
                <span class="text-yellow-800 cursor-pointer hover:text-yellow-900 text-xl" onclick="document.getElementById('blazor-error-ui').style.display='none'">🗙</span>
            </div>
        </div>
    </div>

</body>
</html>
